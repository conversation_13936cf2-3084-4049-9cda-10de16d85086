package com.ysh.bpmn.module.camunda.entity.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-08-15 11:11
 **/
@Data
public class LatestNode {
    // 流程实例id
    private String PROC_INST_ID_;
    // 节点id
    private String TASK_DEF_KEY_;
    // 节点名称
    private String NAME_;
    //节点在最新线中的顺序
    private Integer seqInLatestPath;
    //节点到达时间
    private Date START_TIME_;

    //节点任务
    private List<LatestNodeTask> tasks;
}
