package com.ysh.bpmn.module.camunda.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ysh.bpmn.module.camunda.entity.vo.LatestNode;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface LatestNodeTaskMapper {
    /**
     * 查询最新的路径
     * @param procInstIdQuerySql 参考示例：
     * --         SELECT proc_inst_id
     * --         FROM ysh_qms.non_clinical_deviation
     * --         WHERE group_corp_id = 1
     * @param completed 是否只要审批完成的任务 0-否 1-是
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    List<LatestNode> getLatestNodeTasksByProcInstIdQuerySql(@Param("procInstIdQuerySql") String procInstIdQuerySql,
                                                            @Param("completed") Integer completed);
}
