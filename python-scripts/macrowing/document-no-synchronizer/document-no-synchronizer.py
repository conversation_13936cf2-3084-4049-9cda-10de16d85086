import sched
import time

import redis
from sqlalchemy import Column, String, BigInteger, Integer, DateTime, Boolean
from sqlalchemy import create_engine,text
from sqlalchemy.orm import declarative_base
from sqlalchemy.orm import sessionmaker

Base = declarative_base()


# 鸿翼DMS文档 ORM模型
class DmsFile(Base):
    __tablename__ = 'dms_file'

    file_id = Column(Integer, primary_key=True, autoincrement=True)
    instance_id = Column(Integer, nullable=True)
    folder_id = Column(Integer, nullable=True)
    file_curVerId = Column(Integer, nullable=True)
    file_curVerNumStr = Column(String(50), nullable=True)
    file_curSize = Column(BigInteger, nullable=True)
    file_curCode = Column(String(100), nullable=True)
    file_curRemark = Column(String(2000), nullable=True)
    file_lastVerId = Column(Integer, nullable=True)
    file_lastVerNumStr = Column(String(50), nullable=True)
    file_lastSize = Column(BigInteger, nullable=True)
    file_lastCode = Column(String(100), nullable=True)
    file_lastRemark = Column(String(2000), nullable=True)
    file_guid = Column(String(200), unique=True, nullable=True)
    file_path = Column(String(2000), nullable=True)
    file_name = Column(String(255), nullable=True)
    file_extName = Column(String(32), nullable=True)
    file_totalSize = Column(BigInteger, nullable=True)
    securityLevel_id = Column(Integer, nullable=True)
    file_state = Column(Integer, nullable=True)
    file_currentOperatorId = Column(Integer, nullable=True)
    file_ownerId = Column(Integer, nullable=True)
    file_createType = Column(Integer, nullable=True)
    file_createTime = Column(DateTime, nullable=True)
    file_createOperator = Column(Integer, nullable=True)
    file_modifyTime = Column(DateTime, nullable=True)
    file_modifyOperator = Column(Integer, nullable=True)
    file_archiveTime = Column(DateTime, nullable=True)
    inc_id = Column(String(50), nullable=True)
    file_remark = Column(String(2000), nullable=True)
    file_type = Column(Integer, nullable=True)
    file_lastVerName = Column(String(2000), nullable=True)
    file_lastVerExtName = Column(String(32), nullable=True)
    file_effectiveTime = Column(DateTime, nullable=True)
    file_expirationTime = Column(DateTime, nullable=True)
    file_cipherText = Column(Boolean, nullable=True)
    file_offlinePermission = Column(Integer, nullable=True)
    file_modifyOperatorName = Column(String(200), nullable=True)
    file_createOperatorName = Column(String(200), nullable=True)
    file_deleteTime = Column(DateTime, nullable=True)
    file_deleteOperator = Column(Integer, default=0, nullable=True)
    file_purgeTime = Column(DateTime, nullable=True)
    file_purgeOperator = Column(Integer, default=0, nullable=True)
    file_isDeleted = Column(Boolean, default=0, nullable=True)
    file_isCascadeDelete = Column(Boolean, default=0, nullable=True)
    file_isolation = Column(Boolean, default=0, nullable=True)
    file_indexState = Column(Integer, default=0, nullable=True)
    file_currentOperatorName = Column(String(200), nullable=True)
    file_deleteOperatorName = Column(String(200), nullable=True)
    file_purgeOperatorName = Column(String(200), nullable=True)


def document_no_update_task():
    print("start update document no task...")
    files = (macrowing_session
             .query(DmsFile)
             .where(DmsFile.file_curCode.is_not(None),
                    DmsFile.file_curCode.notlike(''))
             .yield_per(100))
    pipe = redis_connect.pipeline()

    i = 0
    for file in files:
        print(f"{i}. push document no {file.file_curCode} to redis...")
        pipe.sadd('dms:external_document_no', str(file.file_curCode))
        i += 1
    pipe.execute()


# 鸿翼数据库配置
MACROWING_DB_USER = 'root'
MACROWING_DB_PASSWORD = 'root'
MACROWING_DB_HOST = 'localhost'
MACROWING_DB_PORT = '3306'
MACROWING_DB_NAME = 'edoc2v5'

# Redis数据库配置
REDIS_HOST = 'localhost'
REDIS_PASSWORD = 'cfKh@alQm'
REDIS_PORT = 9051
REDIS_DB = 11


#  鸿翼数据库
macrowing_engine = create_engine(
    f'mysql+mysqlconnector://{MACROWING_DB_USER}:{MACROWING_DB_PASSWORD}@{MACROWING_DB_HOST}:{MACROWING_DB_PORT}/{MACROWING_DB_NAME}',
    echo=False)
MacrowingSession = sessionmaker(bind=macrowing_engine)
macrowing_session = MacrowingSession()

#  Redis数据库
redis_connect = redis.Redis(host=REDIS_HOST,
                            port=REDIS_PORT,
                            db=REDIS_DB,
                            decode_responses=True,
                            password=REDIS_PASSWORD)

document_no_update_task()