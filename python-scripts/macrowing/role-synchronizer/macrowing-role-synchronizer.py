from sqlalchemy import create_engine,text
from sqlalchemy.orm import sessionmaker
import logging
import os
from datetime import datetime
from urllib.parse import quote

# 鸿翼数据库配置
MACROWING_DB_USER = 'root'
MACROWING_DB_PASSWORD = 'root'
MACROWING_DB_HOST = 'localhost'
MACROWING_DB_PORT = '3306'
MACROWING_DB_NAME = 'edoc2v5'

# Yessun数据库配置
YEESUN_DB_USER = 'root'
YEESUN_DB_PASSWORD = 'root'
YEESUN_DB_HOST = 'localhost'
YEESUN_DB_PORT = '3306'
YEESUN_DB_NAME = 'ysh_base'

#  鸿翼数据库
macrowing_engine = create_engine(
    f'mysql+mysqlconnector://{MACROWING_DB_USER}:{MACROWING_DB_PASSWORD}@{MACROWING_DB_HOST}:{MACROWING_DB_PORT}/{MACROWING_DB_NAME}',
    echo=False)
MacrowingSession = sessionmaker(bind=macrowing_engine)
macrowing_session = MacrowingSession()

# Yeesun数据库
yeesun_engin = create_engine(
    f'mysql+mysqlconnector://{YEESUN_DB_USER}:{quote(YEESUN_DB_PASSWORD)}@{YEESUN_DB_HOST}:{YEESUN_DB_PORT}/{YEESUN_DB_NAME}',
    echo=False)
YeesunSession = sessionmaker(bind=yeesun_engin)
yeesun_session = YeesunSession()

# 目标租户ID
GROUP_CORP_ID = 1

# 日志保存路劲
LOG_SAVE_PATH = "../../../biohub-modules-qms/scripts/macrowing/role-synchronizer/logs"

# 角色同步SQL语句
ROLE_QUERY_FROM_MACROWING_STATEMENT = text("""
                                           SELECT 
                                                group_id,
                                                group_code,
                                                group_name 
                                           FROM edoc2v5.org_group""")

ROLE_QUERY_FROM_YEESUN_STATEMENT = text("""SELECT 
                                                id,
                                                system_sign,
                                                role_type,
                                                name,
                                                role_code,
                                                external_role_id,
                                                external_role_source,
                                                group_corp_id 
                                        FROM ysh_base.roles 
                                        WHERE external_role_source = 'MACROWING' AND external_role_id = :role_id""")

GROUP_MEMBER_EMAIL_LIST_FROM_MACROWING_STATEMENT = text(""" SELECT ou.user_email
                                                            FROM edoc2v5.org_groupmember ogm
                                                            LEFT JOIN edoc2v5.org_group og ON ogm.group_id = og.group_id
                                                            LEFT JOIN edoc2v5.org_user ou ON ogm.member_id = ou.user_id
                                                            WHERE ou.user_email IS NOT NULL
                                                            AND ogm.group_id = :group_id""")

ROLE_USER_REL_FROM_YEESUN_STATEMENT = text("""
                                        SELECT ur.id,ur.role_id,ur.user_id,r.external_role_id,u.email
                                        FROM ysh_base.user_roles ur
                                        LEFT JOIN ysh_base.roles r ON ur.role_id = r.id
                                        LEFT JOIN ysh_base.users u ON ur.user_id = u.id
                                        WHERE r.external_role_source = 'MACROWING' AND r.external_role_id = :role_id""")
QUERY_USER_ID_BY_EMAIL_FROM_YEESUN_STATEMENT = text("""SELECT id FROM ysh_base.users WHERE email = :email AND group_corp_id = :group_corp_id""")
QUERY_ROLE_ID_BY_EXTERNAL_ROLE_ID_FROM_YEESUN_STATEMENT = text("""SELECT id FROM ysh_base.roles WHERE external_role_id = :external_role_id AND external_role_source = 'MACROWING' AND group_corp_id = :group_corp_id""")

# 获取Logger
def setup_logging():
    logger = logging.getLogger("main_logger")
    logger.setLevel(logging.INFO)

    if not os.path.exists(LOG_SAVE_PATH):
        os.makedirs(LOG_SAVE_PATH)

    current_date = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(LOG_SAVE_PATH, f'{current_date}.log')

    file_handler = logging.FileHandler(log_file,encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    stream_handler = logging.StreamHandler()
    stream_handler.setLevel(logging.INFO)
    stream_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    stream_handler.setFormatter(stream_formatter)
    logger.addHandler(stream_handler)

    return logger

# 角色同步
def synchronize_role():
    # 日志配置
    logger = setup_logging()
    
    # 从鸿翼数据库中查询所有角色
    role_from_macrowing_result = macrowing_session.execute(ROLE_QUERY_FROM_MACROWING_STATEMENT).fetchall()

    for role_from_macrowing_row in role_from_macrowing_result:
        role_id = role_from_macrowing_row[0]
        role_code = role_from_macrowing_row[1]
        role_name = role_from_macrowing_row[2]
        role_from_yeesun = yeesun_session.execute(ROLE_QUERY_FROM_YEESUN_STATEMENT.bindparams(role_id=role_id)).fetchone()
        if role_from_yeesun is None:
            logger.info(f"Role {role_name}({role_code}) not found in Yeesun, creating...")
            yeesun_session.execute(
                text("""
                     INSERT INTO ysh_base.roles
                     (system_sign,role_type,name,role_code,external_role_id,external_role_source,group_corp_id) 
                     VALUES ('qms',0,:role_name,:role_code,:role_id,'MACROWING',:group_corp_id)
                     """),
                  params={"role_name": role_name, "role_code": role_code, "role_id": role_id, "group_corp_id": GROUP_CORP_ID})
            yeesun_session.commit()
            yeesun_session.flush()   
        else:
            if role_from_yeesun.name!= role_name or role_from_yeesun.role_code != role_code or role_from_yeesun.group_corp_id != GROUP_CORP_ID:
                logger.info(f"Role {role_name}({role_code}) found in Yeesun, updating...")
                yeesun_session.execute(
                    text("""
                         UPDATE ysh_base.role 
                         SET name = :role_name, role_code = :role_code, group_corp_id = :group_corp_id 
                         WHERE id = :role_id
                         """),
                      params={"role_name": role_name, "role_code": role_code, "role_id": role_from_yeesun.id, "group_corp_id": GROUP_CORP_ID})
                yeesun_session.commit()
                yeesun_session.flush()          
            else:
                logger.info(f"Role {role_name}({role_code}) found in Yeesun, no need to update.")
        # 查出所有Macrowing数据库中的用户邮箱列表
        group_member_email_list_from_macrowing_result = macrowing_session.execute(GROUP_MEMBER_EMAIL_LIST_FROM_MACROWING_STATEMENT.bindparams(group_id=role_id)).fetchall()
        # 转化为集合
        group_member_email_set = set()
        for row in group_member_email_list_from_macrowing_result:
            if row[0] is not None and row[0] != '':
                group_member_email_set.add(row[0])
        # 查出所有Yeesun数据库中的角色-用户关联（角色ID，用户ID，外部角色ID,用户email）
        role_user_rel_from_yeesun_result = macrowing_session.execute(ROLE_USER_REL_FROM_YEESUN_STATEMENT.bindparams(role_id=role_id)).fetchall()
        # 遍历上一步的到的结果
        insert_list = []
        delete_list = []
        # Yeesun数据库中存在的用户邮箱集合
        yeesun_user_email_set = set()

        for role_user_rel_from_yeesun_row in role_user_rel_from_yeesun_result:
            rel_id = role_user_rel_from_yeesun_row[0]
            role_id = role_user_rel_from_yeesun_row[1]
            user_id = role_user_rel_from_yeesun_row[2]
            external_role_id = role_user_rel_from_yeesun_row[3]
            user_email = role_user_rel_from_yeesun_row[4]

            yeesun_user_email_set.add(user_email)
            # 得到需要删除的角色-用户关联
            if user_email not in group_member_email_set:
                delete_list.append(rel_id)
        
        for macrowing_user_email in group_member_email_set:
            # 得到需要新增的角色-用户关联
            if macrowing_user_email not in yeesun_user_email_set:
                insert_list.append(macrowing_user_email)
        # 新增角色-用户关联
        logger.info(f"Inserting {len(insert_list)} role-user relations for role {role_name}({role_code})...")
        for insert_email in insert_list:

            user_id = yeesun_session.execute(QUERY_USER_ID_BY_EMAIL_FROM_YEESUN_STATEMENT.bindparams(email=insert_email, group_corp_id=GROUP_CORP_ID)).fetchone()
            if user_id is None:
                logger.warning(f"User {insert_email} not found in Yeesun, skipping...")
                continue
            else :
                user_id = user_id[0]

            role_id = yeesun_session.execute(QUERY_ROLE_ID_BY_EXTERNAL_ROLE_ID_FROM_YEESUN_STATEMENT.bindparams(external_role_id=role_id, group_corp_id=GROUP_CORP_ID)).fetchone()
            if role_id is None:
                logger.warning(f"Role {role_name}({role_code}) not found in Yeesun, skipping...")
                continue
            else :
                role_id = role_id[0]
            logger.info(f'Inserting user-role relation for role_id: {role_id}, user_id: {user_id}')
            yeesun_session.execute(
                text("""
                     INSERT INTO ysh_base.user_roles
                     (role_id,user_id,group_corp_id) 
                     VALUES (:role_id,:user_id,:group_corp_id)
                     """),
                  params={"role_id": role_id, "user_id": user_id,"group_corp_id": GROUP_CORP_ID})
            yeesun_session.commit()
            yeesun_session.flush()
        # 删除角色-用户关联
        logger.info(f"Deleting {len(delete_list)} role-user relations for role {role_name}({role_code})...")
        for delete_id in delete_list:
            yeesun_session.execute(
                text("""
                     DELETE FROM ysh_base.user_roles 
                     WHERE id = :rel_id
                     """),
                  params={"rel_id": delete_id})
            yeesun_session.commit()
            yeesun_session.flush()
        # 同步完成
synchronize_role()