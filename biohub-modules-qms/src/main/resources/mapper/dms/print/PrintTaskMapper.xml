<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ysh.qms.module.dms.print.mapper.PrintTaskMapper">
    <resultMap id="print_ledger_list_date_result_map" type="com.ysh.qms.module.dms.print.domain.dto.PrintLedgerListQueryDTO">
        <id property="taskId" column="task_id"/>
        <result property="applyType" column="apply_type" jdbcType="TINYINT"/>
        <result property="applyNo" column="apply_no" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileSuffix" column="file_suffix" jdbcType="VARCHAR"/>
        <result property="targetType" column="target_type" jdbcType="TINYINT"/>
        <result property="targetId" column="target_id" jdbcType="BIGINT"/>
        <result property="copies" column="copies" jdbcType="INTEGER"/>
        <result property="pages" column="pages" jdbcType="INTEGER"/>
        <result property="hasWatermark" column="has_watermark" jdbcType="TINYINT"/>
        <result property="watermarkType" column="watermark_type" jdbcType="VARCHAR"/>
        <result property="paperSize" column="paper_size" jdbcType="VARCHAR"/>
        <result property="paperDirection" column="paper_direction" jdbcType="VARCHAR"/>
        <result property="deviceName" column="device_name" jdbcType="VARCHAR"/>
        <result property="deviceLocation" column="device_location" jdbcType="VARCHAR"/>
        <result property="applyUserId" column="apply_user_id" jdbcType="VARCHAR"/>
        <result property="printDatetime" column="print_datetime" jdbcType="TIMESTAMP"/>
        <collection property="receiveUserIds" column="receiver_user_id" ofType="java.lang.String"/>
    </resultMap>
    <sql id="result_range_where">
        <if test="resultRange = 0">AND pt.group_corp_id = #{groupCorpId}</if>
        <if test="resultRange = 1">AND (pt.apply_user_id = #{requestUserId} OR par.receive_user = #{requestUserId})</if>
        <if test="resultRange = 2">AND pt.apply_user_id = #{requestUserId}</if>
        <if test="resultRange = 3">AND par.receive_user = #{requestUserId}</if>
    </sql>
    <select id="queryRecordPrintListData"
            resultType="com.ysh.qms.module.dms.print.domain.dto.RecordPrintTaskListQueryDTO">
        SELECT DISTINCT
        tasks.id AS task_id,
        tasks.apply_no AS apply_no,
        CASE tasks.target_type
            WHEN 0 THEN db.cn_name
            WHEN 1 THEN da.attachment_name
        END AS file_name,
        CASE tasks.target_type
            WHEN 0 THEN 'PDF'
            WHEN 1 THEN da.file_suffix
        END AS file_suffix,
        tasks.task_state AS state,
        tasks.print_settings->'$.copies' AS copies,
        tasks.print_settings->'$.pages' AS pages,
        tasks.product_information->'$.no' AS product_no,
        tasks.product_information->'$.name' AS product_name,
        tasks.product_information->'$.specification' AS product_specification,
        tasks.product_information->'$.batchNo' AS product_batch_no,
        tasks.print_datetime AS print_datetime
        FROM (
        SELECT pt.*
        FROM ysh_dms.print_task pt
        LEFT JOIN ysh_dms.print_archive_record par ON par.task_id = pt.id
        WHERE pt.delete_flag = 0
        <include refid="result_range_where"/>
        )tasks
        LEFT JOIN ysh_dms.doc_base db ON(db.id = tasks.target_id AND tasks.target_type = 0)
        LEFT JOIN ysh_dms.doc_attachment_rel dar ON (dar.id = tasks.target_id AND tasks.target_type = 1)
        INNER JOIN ysh_dms.doc_attachment da ON da.id = dar.attachment_id
        <where>
            <if test="applyTypes != null and !applyTypes.isEmpty()">
                AND task.apply_type IN
                <foreach collection="applyTypes" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="searchKey != null">AND file_name LIKE CONCAT('%',#{searchKey},'%')</if>
            <if test="startDate != null">AND print_datetime &gt;= #{startDate}</if>
            <if test="endDate != null">AND print_datetime &lt;= #{endDate}</if>
        </where>
    </select>
    <select id="queryFilePrintListData" resultType="com.ysh.qms.module.dms.print.domain.dto.FilePrintTaskListQueryDTO">
        SELECT DISTINCT
        tasks.id AS task_id,
        tasks.apply_no AS apply_no,
        tasks.target_type AS target_type,
        CASE tasks.target_type
            WHEN 0 THEN db.cn_name
            WHEN 1 THEN da.attachment_name
        END AS file_name,
        CASE tasks.target_type
            WHEN 0 THEN db.no
            WHEN 1 THEN da.attachment_no
        END AS file_no,
        CASE tasks.target_type
            WHEN 0 THEN 'PDF'
            WHEN 1 THEN da.file_suffix
        END AS file_suffix,
        tasks.task_state AS state,
        tasks.print_settings->'$.copies' AS copies,
        tasks.print_settings->'$.pages' AS pages,
        tasks.print_datetime AS print_datetime
        FROM (
        SELECT pt.*
        FROM ysh_dms.print_task pt
        LEFT JOIN ysh_dms.print_archive_record par ON par.task_id = pt.id
        WHERE pt.delete_flag = 0
        <include refid="result_range_where"/>
        )tasks
        LEFT JOIN ysh_dms.doc_base db ON(db.id = tasks.target_id AND tasks.target_type = 0)
        LEFT JOIN ysh_dms.doc_attachment_rel dar ON (dar.id = tasks.target_id AND tasks.target_type = 1)
        INNER JOIN ysh_dms.doc_attachment da ON da.id = dar.attachment_id
        <where>
            <if test="applyTypes != null and !applyTypes.isEmpty()">
                AND task.apply_type IN
                <foreach collection="applyTypes" item="item" open="(" close=")" separator=",">#{item}</foreach>
            </if>
            <if test="searchKey != null">AND file_name LIKE CONCAT('%',#{searchKey},'%')</if>
            <if test="startDate != null">AND print_datetime &gt;= #{startDate}</if>
            <if test="endDate != null">AND print_datetime &lt;= #{endDate}</if>
        </where>
    </select>
    <select id="queryTransceiverBalanceListData"
            resultType="com.ysh.qms.module.dms.print.domain.dto.TransceiverBalanceListQueryDTO">
        SELECT DISTINCT
        pt.id AS task_id,
        pt.apply_no AS apply_no,
        CASE pt.target_type
            WHEN 0 THEN db.cn_name
            WHEN 1 THEN da.attachment_name
        END AS file_name,
        CASE pt.target_type
            WHEN 0 THEN db.no
            WHEN 1 THEN da.attachment_no
        END AS file_no,
        CASE pt.target_type
            WHEN 0 THEN 'PDF'
            WHEN 1 THEN da.file_suffix
        END AS file_suffix,
        tasks.task_state AS state,
        pt.print_settings->'$.copies' AS copies,
        pt.print_settings->'$.pages' AS pages,
        pt.print_datetime AS print_datetime,
        COUNT(par.id) AS numer_of_archived,
        FROM ysh_dms.print_task pt
        LEFT JOIN ysh_dms.print_archive_record par ON par.task_id = pt.id
        LEFT JOIN ysh_dms.doc_base db ON(db.id = pt.target_id AND pt.target_type = 0)
        LEFT JOIN ysh_dms.doc_attachment_rel dar ON (dar.id = pt.target_id AND pt.target_type = 1)
        INNER JOIN ysh_dms.doc_attachment da ON da.id = dar.attachment_id
        <where>
            pt.delete_flag = 0
            AND pt.group_corp_id = #{groupCorpId}
            AND pt.apply_type IN (1,2)
            <if test="searchKey != null">AND file_name LIKE CONCAT('%',#{searchKey},'%')</if>
            <if test="startDate != null">AND pt.print_datetime &gt;= #{startDate}</if>
            <if test="endDate != null">AND pt.print_datetime &lt;= #{endDate}</if>
        </where>
        GROUP BY pt.id
        ORDER BY pt.id DESC
    </select>

    <select id="queryPrintLedgerListData" resultMap="print_ledger_list_date_result_map">
        SELECT
            pt.id                                   AS task_id,
            pt.apply_type                           AS apply_type,
            pt.apply_no                             AS apply_no,
            pt.target_type                          AS target_type,
            pt.target_id                            AS target_id,
            CASE pt.target_type
                WHEN 0 THEN db.cn_name
                WHEN 1 THEN da.attachment_name
                END                                 AS file_name,
            CASE pt.target_type
                WHEN 0 THEN 'PDF'
                WHEN 1 THEN da.file_suffix
                END                                 AS file_suffix,
            pt.print_settings->'$.copies'           AS copies,
            pt.print_settings->'$.pages'            AS pages,
            pt.print_settings->'$.appendWatermark'  AS has_watermark,
            pt.print_settings->'$.watermarkType'    AS watermark_type,
            pt.print_settings->'$.watermarkType'    AS watermark_type,
            pt.print_settings->'$.paperSize'        AS paper_size,
            pt.print_settings->'$.paperDirection'   AS paper_direction,
            par.receive_user                        AS receive_user_id,
            pt.apply_user                           AS apply_user_id,
            pt.print_datetime                       AS print_datetime
        FROM ysh_dms.print_task pt
        LEFT JOIN ysh_dms.print_archive_record par ON par.task_id = pt.id
        LEFT JOIN ysh_dms.doc_base db ON(db.id = pt.target_id AND pt.target_type = 0)
        LEFT JOIN ysh_dms.doc_attachment_rel dar ON (dar.id = pt.target_id AND pt.target_type = 1)
        INNER JOIN ysh_dms.doc_attachment da ON da.id = dar.attachment_id
        LEFT JOIN ysh_base.print_devices pd ON pd.id = pt.print_device_id
        WHERE pt.group_corp_id = #{groupCorpId}
        AND pt.delete_flag = 0
    </select>
</mapper>