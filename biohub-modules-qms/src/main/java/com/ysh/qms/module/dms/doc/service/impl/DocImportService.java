package com.ysh.qms.module.dms.doc.service.impl;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yitter.idgen.YitIdHelper;
import com.ysh.base.common.exception.YshNoLogException;
import com.ysh.base.common.exception.YshRuntimeException;
import com.ysh.base.module.audit.service.YshAuditsService;
import com.ysh.base.module.user.domain.dto.DeptCacheDTO;
import com.ysh.base.module.user.domain.dto.UserCacheDTO;
import com.ysh.base.module.user.entity.Depts;
import com.ysh.base.module.user.entity.Users;
import com.ysh.base.module.user.manager.DeptCacheManager;
import com.ysh.base.module.user.manager.UserCacheManager;
import com.ysh.base.module.user.mapper.DeptsMapper;
import com.ysh.base.module.user.mapper.UsersMapper;
import com.ysh.base.util.LocalDateTimeUtil;
import com.ysh.base.util.YshRequestTokenUtil;
import com.ysh.common.module.tenant.GroupRequestUtil;
import com.ysh.common.module.tenant.service.IGroupCorpService;
import com.ysh.qms.common.coding.constant.CodeUseFor;
import com.ysh.qms.common.coding.domain.dto.CodeHistoryAddDTO;
import com.ysh.qms.common.coding.domain.po.CodeHistory;
import com.ysh.qms.common.coding.service.CodeHistoryService;
import com.ysh.qms.common.constant.RequestFormatUrl;
import com.ysh.qms.common.properties.PathProperties;
import com.ysh.qms.module.dms.audit.contants.AuditOptConstants;
import com.ysh.qms.module.dms.audit.util.AuditUtils;
import com.ysh.qms.module.dms.common.util.VersionUtil;
import com.ysh.qms.module.dms.dictionary.constant.VersionFormat;
import com.ysh.qms.module.dms.doc.constant.*;
import com.ysh.qms.module.dms.doc.domain.dto.DocAttachmentUnionRel;
import com.ysh.qms.module.dms.doc.domain.dto.DocImportExcelData;
import com.ysh.qms.module.dms.doc.domain.dto.ImportAttachmentUpdateRequestDTO;
import com.ysh.qms.module.dms.doc.domain.dto.ImportDocumentUpdateRequestDTO;
import com.ysh.qms.module.dms.doc.domain.po.*;
import com.ysh.qms.module.dms.doc.domain.vo.DocImportAttachmentVO;
import com.ysh.qms.module.dms.doc.domain.vo.DocImportExcelParseResult;
import com.ysh.qms.module.dms.doc.domain.vo.DocImportItemAddVO;
import com.ysh.qms.module.dms.doc.domain.vo.DocImportVO;
import com.ysh.qms.module.dms.doc.manager.UserUnfinishedImportTaskManager;
import com.ysh.qms.module.dms.doc.mapper.DocAttachmentMapper;
import com.ysh.qms.module.dms.doc.mapstruct.DocAttachmentDomainMapper;
import com.ysh.qms.module.dms.doc.mapstruct.DocImportAttachmentDomainMapper;
import com.ysh.qms.module.dms.doc.mapstruct.DocImportDomainMapper;
import com.ysh.qms.module.dms.doc.service.*;
import com.ysh.qms.module.dms.file.constant.FileSavePathFormats;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.Collator;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class DocImportService {
    @Resource
    private DeptCacheManager deptCacheManager;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private DocBaseService docBaseService;
    @Resource
    private UsersMapper usersMapper;
    @Resource
    private DeptsMapper deptsMapper;
    @Resource
    private DocFolderService docFolderService;
    @Resource
    private DocAttachmentService docAttachmentService;
    @Resource
    private CodeHistoryService codeHistoryService;
    @Resource
    private DocImportDomainMapper docImportDomainMapper;
    @Resource
    private DocImportAttachmentDomainMapper docImportAttachmentDomainMapper;
    @Resource
    private UserUnfinishedImportTaskManager userUnfinishedImportTaskManager;
    @Resource
    private PathProperties pathProperties;
    @Resource
    private IGroupCorpService groupCorpService;
    @Resource
    private DocNamingRuleService docNamingRuleService;
    @Resource
    private DocAttachmentRelService docAttachmentRelService;
    @Resource
    private UserCacheManager userCacheManager;
    @Resource
    private DocDistributeService docDistributeService;
    @Resource
    private DocFileService docFileService;
    @Resource
    private DocAttachmentMapper docAttachmentMapper;
    @Resource
    private DocAttachmentDomainMapper docAttachmentDomainMapper;
    @Resource
    private YshAuditsService auditsService;

    private final static String UPLOAD_FILE_TEMP_SAVE_PATH_FORMAT = "/import-temp-file/%s/%s/%s.%s";

    public String createImportTask() {
        // 查询这个用户是不是已经 有一条待导入的数据
        String taskKey = userUnfinishedImportTaskManager.get(YshRequestTokenUtil.getRequestUserId());
        if (StringUtils.hasText(taskKey)) {
            throw new YshNoLogException("存在为完成的导入任务，请取消或完成后重新创建");
        }

        // 导入一条空的 mongo记录
        taskKey = UUID.randomUUID().toString();
        DocImport po = new DocImport();
        po.setTaskKey(taskKey)
                .setGroupCorpId(YshRequestTokenUtil.getRequestGroupCorpId())
                .setImporterId(YshRequestTokenUtil.getRequestUserId());
        mongoTemplate.save(po);

        userUnfinishedImportTaskManager.set(taskKey, YshRequestTokenUtil.getRequestUserId());
        return taskKey;
    }

    /**
     * 解析导入Excel文件
     *
     * @param file 导入Excel文件
     * @return 解析结果
     */
    public DocImportExcelParseResult parseImportExcel(String taskKey, MultipartFile file) {

        if (file != null && !file.isEmpty()) {
            Map<String, Depts> deptNameMap = deptsMapper.selectList(Wrappers.lambdaQuery(Depts.class)
                            .eq(Depts::getGroupCorpId, YshRequestTokenUtil.getRequestGroupCorpId()))
                    .stream()
                    .collect(Collectors.toMap(Depts::getName, Function.identity(), (k1, k2) -> null));

            String importerId = YshRequestTokenUtil.getRequestUserId();
            Map<String, DocImport> importedDocuments = new HashMap<>();
            AtomicInteger successCounter = new AtomicInteger();
            AtomicInteger failCounter = new AtomicInteger();
            List<String> errors = new ArrayList<>();
            try (InputStream fis = file.getInputStream()) {
                EasyExcel.read(fis, DocImportExcelData.class, new PageReadListener<DocImportExcelData>(list -> {
                    for (DocImportExcelData excelData : list) {
                        try {
                            if (!StringUtils.hasText(excelData.getBelongDocumentNo())) {
                                DocImport po = new DocImport();
                                CodeHistory codeHistory = codeHistoryService.getOne(Wrappers.lambdaQuery(CodeHistory.class)
                                        .eq(CodeHistory::getGroupCorpId, YshRequestTokenUtil.getRequestGroupCorpId())
                                        .eq(CodeHistory::getTargetCode, excelData.getNo())
                                        .eq(CodeHistory::getUsed, true));
                                if (codeHistory != null) {
                                    CodeUseFor codeUseFor = CodeUseFor.getByOrdinal(codeHistory.getUseFor());
                                    if (codeUseFor == CodeUseFor.DOCUMENT) {
                                        if (docBaseService.count(Wrappers.lambdaQuery(DocBase.class)
                                                .eq(DocBase::getGroupCorpId, YshRequestTokenUtil.getRequestGroupCorpId())
                                                .eq(DocBase::getVersion, excelData.getVersion())
                                                .eq(DocBase::getNo, excelData.getNo())) > 0) {
                                            errors.add("编号：" + excelData.getNo() + "版本：" + excelData.getVersion() + "的文件已经存在");
                                            failCounter.incrementAndGet();
                                            continue;
                                        }
                                    } else {
                                        if (VersionUtil.isValidVersion(excelData.getNo())) {
                                            errors.add("编号：" + excelData.getNo() + "格式错误");
                                            failCounter.incrementAndGet();
                                            continue;
                                        }
                                        int versionNumber = VersionUtil.parseVersion2Int(excelData.getNo());
                                        if (docAttachmentMapper.countByNoAndVersion(YshRequestTokenUtil.getRequestGroupCorpId(),
                                                excelData.getNo(),
                                                VersionUtil.convertInt2Version(versionNumber, VersionFormat.FLOAT),
                                                VersionUtil.convertInt2Version(versionNumber, VersionFormat.INTEGER)) > 0) {
                                            errors.add("编号：" + excelData.getNo() + "已被占用");
                                            failCounter.incrementAndGet();
                                            continue;
                                        }
                                    }
                                }
                                if (!StringUtils.hasText(excelData.getDrafterType())) {
                                    errors.add("文件" + excelData.getNo() + "未指定起草人类型");
                                    failCounter.incrementAndGet();
                                    continue;
                                }
                                switch (excelData.getDrafterType()) {
                                    case "人员":
                                        po.setDrafterType(DrafterType.PERSONNEL);
                                        break;
                                    case "部门":
                                        po.setDrafterType(DrafterType.DEPARTMENT);
                                        break;
                                    default:
                                        errors.add(excelData.getNo() + "使用了错误的起草人类型取值");
                                        failCounter.incrementAndGet();
                                        continue;
                                }
                                List<Users> users = usersMapper.selectList(Wrappers.lambdaQuery(Users.class)
                                        .eq(Users::getGroupCorpId, YshRequestTokenUtil.getRequestGroupCorpId())
                                        .eq(Users::getName, excelData.getDrafter()));
                                if (!users.isEmpty()) {
                                    Users user = users.get(0);
                                    po.setDrafterId(user.getId());
                                }

                                Optional.ofNullable(deptNameMap.get(excelData.getDraftDepartment()))
                                        .map(Depts::getId)
                                        .ifPresent(po::setDraftDepartmentId);

                                if (excelData.getPath() != null) {
                                    List<DocFolder> folders = docFolderService.list(Wrappers.lambdaQuery(DocFolder.class)
                                            .eq(DocFolder::getGroupCorpId, YshRequestTokenUtil.getRequestGroupCorpId())
                                            .eq(DocFolder::getFolderPath, excelData.getPath()));
                                    if (folders.size() == 1) {
                                        po.setFolderId(folders.get(0).getId());
                                    }
                                }

                                Optional.ofNullable(excelData.getDistributeDepartment())
                                        .ifPresent(names -> {
                                            Set<Long> departmentIds = new HashSet<>();
                                            for (String name : names) {
                                                Optional.ofNullable(deptNameMap.get(name))
                                                        .map(Depts::getId)
                                                        .ifPresent(departmentIds::add);
                                            }
                                            po.setDistributeDepartmentIds(departmentIds);
                                        });
                                po
                                        .setTaskKey(taskKey)
                                        .setImporterId(importerId)
                                        .setGroupCorpId(YshRequestTokenUtil.getRequestGroupCorpId())
                                        .setCorpId(YshRequestTokenUtil.getRequestCorpId())
                                        .setDocumentNo(excelData.getNo())
                                        .setDocumentVersion(excelData.getVersion())
                                        .setCnName(excelData.getCnName())
                                        .setEnName(excelData.getEnName())
                                        .setEffectDate(excelData.getEffectDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                                        .setExpiryDate(excelData.getExpiryDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                                        .setApplyReason(excelData.getApplyReason());

                                importedDocuments.put(excelData.getNo(), po);
                                successCounter.getAndIncrement();
                            } else {
                                DocImport belongDocument = importedDocuments.get(excelData.getBelongDocumentNo());
                                if (belongDocument != null) {
                                    CodeHistory codeHistory = codeHistoryService.getOne(Wrappers.lambdaQuery(CodeHistory.class)
                                            .eq(CodeHistory::getGroupCorpId, YshRequestTokenUtil.getRequestGroupCorpId())
                                            .eq(CodeHistory::getTargetCode, excelData.getNo())
                                            .eq(CodeHistory::getUsed, true));
                                    if (codeHistory != null) {
                                        CodeUseFor codeUseFor = CodeUseFor.getByOrdinal(codeHistory.getUseFor());
                                        if (codeUseFor != CodeUseFor.ATTACHMENT) {
                                            errors.add("编号：" + excelData.getNo() + "已被占用");
                                            failCounter.incrementAndGet();
                                            continue;
                                        }
                                    }
                                    int versionNumber = VersionUtil.parseVersion2Int(excelData.getVersion());
                                    if (docAttachmentMapper.countByNoAndVersion(YshRequestTokenUtil.getRequestCorpId(),
                                            excelData.getNo(),
                                            VersionUtil.convertInt2Version(versionNumber, VersionFormat.FLOAT),
                                            VersionUtil.convertInt2Version(versionNumber, VersionFormat.INTEGER)) > 0) {
                                        errors.add("编号：" + excelData.getNo() + " 版本：" + excelData.getVersion() + " 的附件已经存在");
                                        continue;
                                    }

                                    DocImport.Attachment attachment = belongDocument.addAttachment(excelData.getNo());
                                    if (attachment == null) {
                                        return;
                                    }
                                    List<Users> users = usersMapper.selectList(Wrappers.lambdaQuery(Users.class)
                                            .eq(Users::getGroupCorpId, YshRequestTokenUtil.getRequestGroupCorpId())
                                            .eq(Users::getName, excelData.getDrafter()));
                                    if (!users.isEmpty()) {
                                        Users user = users.get(0);
                                        attachment.setDrafterId(user.getId());
                                    }

                                    Optional.ofNullable(deptNameMap.get(excelData.getDraftDepartment()))
                                            .map(Depts::getId)
                                            .ifPresent(attachment::setDraftDepartmentId);
                                    attachment
                                            .setAttachmentNo(excelData.getNo())
                                            .setAttachmentVersion(excelData.getVersion())
                                            .setAttachmentName(excelData.getCnName())
                                            .setEffectDate(excelData.getEffectDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate())
                                            .setApplyReason(excelData.getApplyReason());
                                    successCounter.getAndIncrement();
                                } else {
                                    errors.add("编号：" + excelData.getNo() + "版本：" + excelData.getVersion() + "的附件找不到所属文件");
                                    failCounter.getAndIncrement();
                                }
                            }
                        } catch (Exception ex) {
                            log.error("预料之外的导入信息错误：{}", ex.getMessage(), ex);
                            failCounter.getAndIncrement();
                        }
                    }
                })).excelType(ExcelTypeEnum.XLSX).sheet().doRead();
            } catch (IOException e) {
                throw new YshRuntimeException("导入数据读取失败", "导入数据读取失败", e);
            }
            DocImportExcelParseResult result = new DocImportExcelParseResult();

            if (importedDocuments.isEmpty()) {
                result
                        .setTaskKey(null)
                        .setSuccessCount(0)
                        .setFailCount(failCounter.get())
                        .setErrors(errors);
                return result;
            }
            mongoTemplate.insertAll(importedDocuments.values());
            userUnfinishedImportTaskManager.set(taskKey, YshRequestTokenUtil.getRequestUserId());

            result
                    .setTaskKey(taskKey)
                    .setSuccessCount(successCounter.get())
                    .setFailCount(failCounter.get())
                    .setErrors(errors);
            return result;
        } else {
            throw new YshNoLogException("导入的Excel文件为空");
        }
    }

    /**
     * 获取用户的未完成导入任务key
     *
     * @param requestUserId 请求用户ID
     * @return 用户的未完成导入任务key
     */
    public String getUserUnfinishedImportTask(String requestUserId) {
        return userUnfinishedImportTaskManager.get(requestUserId);
    }

    /**
     * 获取导入任务列表
     *
     * @param taskKey 任务Key
     * @return 导入文档/附件列表
     */
    public List<DocImportVO> getImportTaskList(String taskKey) {
        return mongoTemplate.find(
                        Query.query(Criteria.where("taskKey").is(taskKey)),
                        DocImport.class)
                .stream()
                .map(this::convertPO2VO)
                .collect(Collectors.toList());
    }

    private DocImportVO convertPO2VO(DocImport o) {
        DocImportVO vo = docImportDomainMapper.convertPO2VO(o);
        Optional.ofNullable(vo.getDrafterId())
                .map(userCacheManager::get)
                .ifPresent(user -> vo.setDrafterName(user.getName()));
        Optional.ofNullable(vo.getDraftDepartmentId())
                .map(deptCacheManager::get)
                .ifPresent(dept -> vo.setDraftDepartmentName(dept.getName()));
        Optional.ofNullable(vo.getDistributeDepartmentIds())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(deptIds -> vo.setDistributeDepartmentNames(deptIds.stream()
                        .map(deptCacheManager::get)
                        .map(DeptCacheDTO::getName)
                        .collect(Collectors.toSet())));
        Optional.ofNullable(vo.getFolderId())
                .map(docFolderService::getById)
                .ifPresent(folder -> vo.setFolderPath(docFolderService.getFolderPath(folder.getId())));
        Optional.ofNullable(o.getSavePath())
                .ifPresent(savePath -> vo.setPreviewUrl(RequestFormatUrl.IMPORT_DOCUMENT_DOWNLOAD
                        .replace(RequestFormatUrl.Tag.HOST, GroupRequestUtil.getRequestUrlPrefix() + "/api")
                        .replace(RequestFormatUrl.Tag.PATH_VARIABLE, vo.getId())
                        .replace(RequestFormatUrl.Tag.TOKEN, Objects.requireNonNull(YshRequestTokenUtil.getRequestToken()))));
        Optional.ofNullable(o.getAttachments())
                .ifPresent(attachments -> vo
                        .setAttachments(
                                attachments
                                        .values()
                                        .stream()
                                        .map(docImportAttachmentDomainMapper::convertPO2VO)
                                        .peek(attachment -> {
                                            if (attachment.getAttachmentNo() == null) {
                                                attachment.setAttachmentNo("");
                                            }
                                            Optional.ofNullable(attachment.getDrafterId())
                                                    .map(userCacheManager::get)
                                                    .ifPresent(user -> attachment.setDrafterName(user.getName()));
                                            Optional.ofNullable(attachment.getDraftDepartmentId())
                                                    .map(deptCacheManager::get)
                                                    .ifPresent(dept -> attachment.setDraftDepartmentName(dept.getName()));
                                            Optional.ofNullable(attachment.getUploaded())
                                                    .filter(uploaded -> uploaded)
                                                    .ifPresent(uploaded -> attachment.setPreviewUrl(RequestFormatUrl.IMPORT_ATTACHMENT_DOWNLOAD
                                                            .replace(RequestFormatUrl.Tag.HOST, GroupRequestUtil.getRequestUrlPrefix() + "/api")
                                                            .replace(RequestFormatUrl.Tag.ID, vo.getId())
                                                            .replace(RequestFormatUrl.Tag.ATTACHMENT_ID, attachment.getId())
                                                            .replace(RequestFormatUrl.Tag.TOKEN, Objects.requireNonNull(YshRequestTokenUtil.getRequestToken()))));
                                        })
                                        .sorted(Comparator.comparing(DocImportAttachmentVO::getAttachmentNo, Collator.getInstance(Locale.ENGLISH)))
                                        .collect(Collectors.toList())));

        return vo;
    }

    /**
     * 删除导入文档/附件记录
     *
     * @param documentId   文档ID
     * @param attachmentId 附件ID（可为空，不为空时删除附件记录）
     */
    public void removeTaskItem(String documentId, String attachmentId) {
        Query query = Query.query(Criteria.where("_id").is(documentId));
        DocImport one = mongoTemplate.findOne(query, DocImport.class);
        if (one == null) {
            return;
        }
        if (attachmentId == null) {
            mongoTemplate.remove(query, DocImport.class);
        } else {
            Map<String, DocImport.Attachment> attachments = one.getAttachments();
            if (attachments == null) {
                return;
            }
            DocImport.Attachment attachment = attachments.get(attachmentId);
            if (attachment == null) {
                return;
            }
            mongoTemplate.updateMulti(Query.query(Criteria.where("_id").is(documentId)), new Update().unset("attachments." + attachmentId), DocImport.class);
        }
    }

    /**
     * 完成导入任务
     *
     * @param taskKey 任务Key
     */
    public void finishImportTask(String taskKey) {
        Query query = Query.query(Criteria.where("taskKey").is(taskKey));


        List<DocImport> docImports = mongoTemplate.find(query, DocImport.class);

        // 检查是否存在重复的编号+版本号
        checkDuplicateDocuments(docImports);
        for (DocImport docImport : docImports) {
            DocBase po;
            DocFolder folder = Optional
                    .ofNullable(docImport.getFolderId())
                    .map(docFolderService::getById)
                    .orElseThrow(() -> new YshNoLogException("指定的文件路径已经不存在"));
            String fullName = Optional.ofNullable(folder)
                    .map(DocFolder::getNamingRuleId)
                    .map(namingRuleId -> docNamingRuleService.getFullNameById(namingRuleId, docImport.getCnName(), docImport.getEnName(), docImport.getDocumentNo(), docImport.getDocumentVersion()))
                    .orElse(docImport.getCnName());

            po = new DocBase()
                    .setGroupCorpId(docImport.getGroupCorpId())
                    .setCorpId(docImport.getCorpId())
                    .setCnName(Optional.ofNullable(docImport.getCnName()).filter(StringUtils::hasText).orElseThrow(() -> new YshNoLogException("存在未指定名称的文件")))
                    .setEnName(Optional.ofNullable(docImport.getEnName()).orElse("-"))
                    .setNo(Optional.ofNullable(docImport.getDocumentNo()).filter(StringUtils::hasText).orElseThrow(() -> new YshNoLogException("存在未指定编号的文件")))
                    .setVersion(Optional.ofNullable(docImport.getDocumentVersion()).orElseThrow(() -> new YshNoLogException("存在未指定版本号的文件")))
                    .setEffectDate(Optional.ofNullable(docImport.getEffectDate()).orElseThrow(() -> new YshNoLogException("存在未指定生效日期的文件")))
                    .setExpiryDate(docImport.getExpiryDate())
                    .setFolderId(Optional.ofNullable(docImport.getFolderId()).map(docFolderService::getById).map(DocFolder::getId).orElseThrow(() -> new YshNoLogException("存在未指定文件路径的文件")))
                    .setFullName(fullName)
                    .setDrafterType(Optional.ofNullable(docImport.getDrafterType()).map(Enum::ordinal).orElseThrow(() -> new YshNoLogException("存在未指定起草人类型的文件")))
                    .setDrafterId(Optional.ofNullable(docImport.getDrafterId()).orElseThrow(() -> new YshNoLogException("存在尚未设置文件起草人的文件")))
                    .setDraftDept(Optional.ofNullable(docImport.getDraftDepartmentId()).orElseThrow(() -> new YshNoLogException("存在尚未设置文件起草部门的文件")))
                    .setFromImport(true)
                    .setReason(docImport.getApplyReason());
            List<DocBase> existsVersions = docBaseService.list(Wrappers.lambdaQuery(DocBase.class)
                    .eq(DocBase::getCorpId, docImport.getCorpId())
                    .eq(DocBase::getNo, docImport.getDocumentNo()));
            existsVersions.add(po);
            existsVersions.sort((o1, o2) -> {
                BigDecimal b1 = new BigDecimal(o1.getVersion());
                BigDecimal b2 = new BigDecimal(o2.getVersion());
                return b2.compareTo(b1);
            });
            if (existsVersions.size() > 1) {
                for (int i = 0; i < existsVersions.size(); i++) {
                    DocBase o = existsVersions.get(i);
                    if (i == 0) {
                        if (o.getExpiryDate() != null && !o.getExpiryDate().isAfter(LocalDate.now())) {
                            o.setState(DocState.EXPIRY.getValue());
                        } else {
                            o.setState(DocState.EFFECT.getValue());
                        }
                    } else {
                        o.setState(DocState.ABOLISH.getValue());
                    }
                    if (o.getId() == null)
                        docBaseService.save(o);
                    Optional.of(i + 1)
                            .filter(index -> index < existsVersions.size())
                            .map(existsVersions::get)
                            .map(DocBase::getId)
                            .ifPresent(o::setParentId);
                }
                docBaseService.saveOrUpdateBatch(existsVersions);
            } else {
                po.setState(DocState.EFFECT.getValue());
                docBaseService.save(po);
            }

            Set<Long> distributeDepartmentIds = deptCacheManager.getParentsId(docImport.getDistributeDepartmentIds());
            List<DocDistribute> distributes = new ArrayList<>();
            for (Long distributeDepartmentId : distributeDepartmentIds) {
                DocDistribute distribute = new DocDistribute();
                distribute
                        .setDocId(po.getId())
                        .setDeptId(distributeDepartmentId);
                distributes.add(distribute);
            }
            if (!distributes.isEmpty()) {
                docDistributeService.saveBatch(distributes);
            }
            assert folder != null;
            if (existsVersions.size() <= 1) {
                CodeHistoryAddDTO codeHistoryAddDTO = new CodeHistoryAddDTO();
                codeHistoryAddDTO
                        .setGroupCorpId(po.getGroupCorpId())
                        .setCodingRuleId(folder.getDocCodingRuleId())
                        .setUseFor(CodeUseFor.DOCUMENT)
                        .setCode(po.getNo());
                codeHistoryService.addCodeHistory(codeHistoryAddDTO);
            }

            String pdfPath = pathProperties.getRoot() + String.format(FileSavePathFormats.EFFECT_FILE, po.getGroupCorpId(), po.getId() % 10, po.getId(), po.getId(), "pdf");
            File file = new File(docImport.getSavePath());
            if (!file.exists()) {
                throw new YshNoLogException("生效文件未上传");
            }
            FileUtil.move(file, new File(pdfPath), true);
            DocFile docFilePDF = new DocFile();
            docFilePDF
                    .setGroupCorpId(po.getGroupCorpId())
                    .setCorpId(po.getCorpId())
                    .setFileType(DocFileType.EFFECT_PDF.ordinal())
                    .setDocId(po.getId())
                    .setFilename(po.getId().toString())
                    .setSuffix("pdf")
                    .setSavePath(pdfPath)
                    .setVersion(VersionUtil.getVersionNumber(po.getVersion()));
            docFileService.save(docFilePDF);

            LocalDate now = LocalDate.now();

            List<DocAttachment> updateAttachments = new ArrayList<>();
            List<DocAttachmentRel> updateAttachmentRels = new ArrayList<>();
            Optional.ofNullable(docImport.getAttachments())
                    .ifPresent(attachments -> attachments.forEach((key, attachment) -> {
                        List<DocAttachmentUnionRel> existsAttachments = docAttachmentMapper.selectUnionListByNo(po.getCorpId(), attachment.getAttachmentNo());

                        DocAttachmentUnionRel currentAttachment = new DocAttachmentUnionRel()
                                .setGroupCorpId(po.getGroupCorpId())
                                .setCorpId(po.getCorpId())
                                .setAttachmentId(null)
                                .setAttachmentRelId(null)
                                .setAncestorId(null)
                                .setParentId(null)
                                .setDocId(po.getId())
                                .setAttachmentName(attachment.getAttachmentName())
                                .setAttachmentNo(attachment.getAttachmentNo())
                                .setAttachmentVersion(attachment.getAttachmentVersion())
                                .setOriginFilename(attachment.getOriginalFilename())
                                .setDrafterId(attachment.getDrafterId())
                                .setDraftDepartmentId(attachment.getDraftDepartmentId())
                                .setFileSuffix(attachment.getSuffix())
                                .setEffectDate(attachment.getEffectDate())
                                .setExpiryDate(po.getExpiryDate())
                                .setRelType(DocAttachmentRelType.FROM_IMPORT.ordinal());
                        existsAttachments.add(currentAttachment);
                        existsAttachments.sort((o1, o2) -> {
                            int v1 = VersionUtil.parseVersion2Int(o1.getAttachmentVersion());
                            int v2 = VersionUtil.parseVersion2Int(o2.getAttachmentVersion());
                            // 倒序排列
                            if (v1 == v2) {
                                return Math.toIntExact((Optional.ofNullable(o2.getAttachmentId()).orElse(Long.MAX_VALUE) - Optional.ofNullable(o1.getAttachmentId()).orElse(Long.MAX_VALUE)));
                            }
                            return v2 - v1;
                        });

                        for (DocAttachmentUnionRel existsAttachment : existsAttachments) {
                            if (existsAttachment.getAttachmentId() == null && existsAttachment.getAttachmentRelId() == null) {
                                saveNewAttachment(attachment, existsAttachment, po, now);
                            }
                        }
                        for (int i = 0; i < existsAttachments.size(); i++) {
                            DocAttachmentUnionRel docAttachmentUnionRel = existsAttachments.get(i);
                            DocAttachment docAttachment = docAttachmentDomainMapper.splitAttachmentUnionRel2Attachment(docAttachmentUnionRel);
                            DocAttachmentRel docAttachmentRel = docAttachmentDomainMapper.splitAttachmentUnionRel2AttachmentRel(docAttachmentUnionRel);
                            Optional
                                    .of(i + 1)
                                    .filter(index -> index < existsAttachments.size())
                                    .map(existsAttachments::get)
                                    .map(DocAttachmentUnionRel::getAttachmentId)
                                    .ifPresent(docAttachment::setParentId);
                            Optional.of(existsAttachments.size() - 1)
                                    .map(existsAttachments::get)
                                    .map(DocAttachmentUnionRel::getAttachmentId)
                                    .ifPresent(docAttachment::setAncestorId);
                            if (i == 0) {
                                if (docAttachmentRel.getExpiryDate() != null && !docAttachmentRel.getExpiryDate().isAfter(LocalDate.now())) {
                                    docAttachmentRel.setState(DocAttachmentState.EXPIRY.getValue());
                                } else {
                                    docAttachmentRel.setState(DocAttachmentState.EFFECT.getValue());
                                }
                            } else {
                                docAttachmentRel.setState(DocAttachmentState.ABOLISH.getValue());
                            }
                            updateAttachments.add(docAttachment);
                            updateAttachmentRels.add(docAttachmentRel);

                            if (existsAttachments.size() <= 1) {
                                CodeHistoryAddDTO codeHistoryAddDTO = new CodeHistoryAddDTO();

                                codeHistoryAddDTO
                                        .setGroupCorpId(po.getGroupCorpId())
                                        .setCodingRuleId(folder.getRecordCodingRuleId())
                                        .setUseFor(CodeUseFor.ATTACHMENT)
                                        .setCode(docAttachment.getAttachmentNo());
                                codeHistoryService.addCodeHistory(codeHistoryAddDTO);
                            }
                        }
                        Optional.of(updateAttachments)
                                .filter(o -> !o.isEmpty())
                                .ifPresent(docAttachmentService::updateBatchById);
                        Optional.of(updateAttachmentRels)
                                .filter(o -> !o.isEmpty())
                                .ifPresent(docAttachmentRelService::updateBatchById);
                    }));


            auditsService.save(AuditUtils.makeBuilder(Objects.requireNonNull(YshRequestTokenUtil.getRequestUserBO()))
                    .groupCorpId(YshRequestTokenUtil.getRequestGroupCorpId())
                    .version(Double.valueOf(VersionUtil.getVersionNumber(po.getVersion())))
                    .versionNo(po.getVersion())
                    .businessId(String.valueOf(po.getId()))
                    .opt(AuditOptConstants.IMPORT_FILE)
                    .item(po.getNo())
                    .description("导入了生效文件" + po.getFullName())
                    .eventDate(LocalDateTime.now())
                    .build());
        }
        userUnfinishedImportTaskManager.remove(YshRequestTokenUtil.getRequestUserId());
    }

    /**
     * 保存一个新的导入附件记录
     *
     * @param attachment       导入附件PO
     * @param existsAttachment 已存在的附件记录
     * @param po               文档PO
     * @param now              当前日期
     */
    private void saveNewAttachment(DocImport.Attachment attachment, DocAttachmentUnionRel existsAttachment, DocBase po, LocalDate now) {
        DocAttachment docAttachment = docAttachmentDomainMapper.splitAttachmentUnionRel2Attachment(existsAttachment);
        DocAttachmentRel docAttachmentRel = docAttachmentDomainMapper.splitAttachmentUnionRel2AttachmentRel(existsAttachment);
        docAttachmentService.save(docAttachment);

        String savePath = pathProperties.getRoot() + String.format(FileSavePathFormats.ATTACHMENT,
                po.getGroupCorpId(),
                po.getCorpId(),
                now.format(LocalDateTimeUtil.YM),
                po.getId(),
                docAttachment.getId(),
                docAttachment.getFileSuffix());
        Optional.ofNullable(attachment.getSavePath())
                .filter(StringUtils::hasText)
                .orElseThrow(() -> new YshNoLogException("附件未上传"));
        FileUtil.move(new File(attachment.getSavePath()), new File(savePath), true);
        docAttachment.setSavePath(savePath);
        docAttachmentService.updateById(docAttachment);
        docAttachmentRel
                .setRelType(DocAttachmentRelType.FROM_IMPORT.ordinal())
                .setAttachmentId(docAttachment.getId());

        if (docAttachmentRel.getExpiryDate() != null && !docAttachmentRel.getExpiryDate().isAfter(LocalDate.now())) {
            docAttachmentRel.setState(DocAttachmentState.EXPIRY.getValue());
        } else {
            docAttachmentRel.setState(DocAttachmentState.EFFECT.getValue());
        }
        docAttachmentRelService.save(docAttachmentRel);
        existsAttachment
                .setAttachmentId(docAttachment.getId())
                .setAttachmentRelId(docAttachmentRel.getId());

        CompletableFuture.runAsync(() -> docAttachmentService.getAttachmentPDF(docAttachment.getId()));
    }


    /**
     * 上传导入文档内容文件
     *
     * @param id   导入文档ID
     * @param file 内容文件
     */
    public void uploadImportDocument(String id, MultipartFile file) {
        String suffix = FileUtil.getSuffix(file.getOriginalFilename());
        if (suffix == null || !Objects.equals(suffix.toLowerCase(), "pdf")) {
            throw new YshNoLogException("不支持的生效文件格式");
        }
        LocalDate now = LocalDate.now();
        Optional.ofNullable(mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), DocImport.class))
                .ifPresent(one -> {
                    try {
                        String savePath = pathProperties.getRoot() + String.format(UPLOAD_FILE_TEMP_SAVE_PATH_FORMAT, now.getYear() + "-" + now.getMonthValue(), id, id, "pdf");
                        File f = new File(savePath);
                        FileUtil.mkParentDirs(f);
                        file.transferTo(f);
                        mongoTemplate.updateFirst(Query.query(Criteria.where("_id").is(id)), Update.update("savePath", savePath), DocImport.class);
                    } catch (IOException e) {
                        throw new YshRuntimeException("上传失败", "转运导入的生效文件出错", e);
                    }
                });
    }

    /**
     * 上传导入附件的内容文件
     *
     * @param id           导入文档ID
     * @param attachmentId 导入附件ID
     * @param file         内容文件
     */
    public void uploadImportAttachment(String id, String attachmentId, MultipartFile file) {
        // TODO 附件格式检查
        String suffix = FileUtil.getSuffix(file.getOriginalFilename());
        LocalDate now = LocalDate.now();
        Optional.ofNullable(mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), DocImport.class))
                .ifPresent(one -> {
                    String savePath = pathProperties.getRoot() + String.format(UPLOAD_FILE_TEMP_SAVE_PATH_FORMAT, now.getYear() + "-" + now.getMonthValue(), id, attachmentId, suffix);
                    Update update = new Update();
                    update
                            .set("attachments." + attachmentId + ".suffix", suffix)
                            .set("attachments." + attachmentId + ".savePath", savePath)
                            .set("attachments." + attachmentId + ".originalFilename", file.getOriginalFilename());
                    mongoTemplate.updateFirst(
                            Query.query(Criteria.where("_id").is(id)),
                            update,
                            DocImport.class);
                    try {
                        File f = new File(savePath);
                        FileUtil.mkParentDirs(f);
                        file.transferTo(new File(savePath));
                    } catch (IOException e) {
                        throw new YshRuntimeException("上传失败", "转运导入的附件出错", e);
                    }
                });
    }

    /**
     * 删除导入任务
     *
     * @param userId 用户的ID
     */
    public void removeImportTask(String userId) {
        userUnfinishedImportTaskManager.remove(userId);
    }

    public DocImport getById(String id) {
        return mongoTemplate.findOne(Query.query(Criteria.where("_id").is(id)), DocImport.class);
    }

    /**
     * 添加一条空白的导入文档/附件记录
     *
     * @param taskKey 任务Key
     * @param id      文档ID(为空则为新增文档,否则为新增附件)
     * @return 新增的文档/附件ID
     */
    public DocImportItemAddVO addTaskItem(String taskKey, String id) {
        DocImportItemAddVO vo = new DocImportItemAddVO();
        if (id == null) {
            DocImport docImport = new DocImport();
            docImport
                    .setGroupCorpId(YshRequestTokenUtil.getRequestGroupCorpId())
                    .setTaskKey(taskKey);
            mongoTemplate.save(docImport);
            vo.setDocumentId(docImport.getId().toString());
        } else {
            Query query = Query.query(Criteria.where("_id").is(id));
            Update update = new Update();
            long attachmentId = YitIdHelper.nextId();
            DocImport.Attachment attachment = new DocImport.Attachment();
            attachment.setId(String.valueOf(attachmentId));
            update.set("attachments." + attachmentId, attachment);
            mongoTemplate.updateFirst(query, update, DocImport.class);
            vo.setAttachmentId(attachmentId);
        }
        return vo;
    }

    /**
     * 更新导入文档信息
     *
     * @param dto 请求对象
     * @return 更改后的文档信息VO
     */
    public DocImportVO updateDocumentItem(ImportDocumentUpdateRequestDTO dto) {
        Query query = Query.query(Criteria.where("_id").is(dto.getId()));
        Update update = new Update();
        DocImport docImport = getById(dto.getId());
        DocImportVO vo = convertPO2VO(docImport);
        Optional.ofNullable(dto.getNo())
                .filter(StringUtils::hasText)
                .ifPresent(no -> {
                    update.set("documentNo", no);
                    vo.setDocumentNo(no);
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });

        String documentVersion = Optional.ofNullable(dto.getVersion())
                .filter(StringUtils::hasText)
                .orElse(null);
        if (documentVersion != null) {
            if (VersionUtil.isValidVersion(documentVersion)) {
                update.set("documentVersion", documentVersion);
                vo.setDocumentVersion(documentVersion);
                mongoTemplate.updateFirst(query, update, DocImport.class);

            } else {
                throw new YshNoLogException("版本号格式错误");
            }
        }

        Optional.ofNullable(dto.getCnName())
                .filter(StringUtils::hasText)
                .ifPresent(cnName -> {
                    update.set("cnName", cnName);
                    vo.setCnName(cnName);
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });
        Optional.ofNullable(dto.getEnName())
                .ifPresent(enName -> {
                    update.set("enName", enName);
                    vo.setEnName(enName);
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });
        Optional.ofNullable(dto.getEffectDate())
                .ifPresent(effectDate -> {
                    update.set("effectDate", effectDate);
                    vo.setEffectDate(effectDate);
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });

        Optional.ofNullable(dto.getExpiryDate())
                .ifPresent(expiryDate -> {
                    update.set("expiryDate", expiryDate);
                    vo.setExpiryDate(expiryDate);
                    mongoTemplate.updateFirst(query, update, DocImport.class);
                });

        Optional.ofNullable(dto.getDrafterType())
                .map(DrafterType::valueOf)
                .ifPresent(o -> {
                    update.set("drafterType", o.name());
                    vo.setDrafterType(o.name());
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });

        Optional.ofNullable(dto.getDrafterId())
                .map(userCacheManager::get)
                .ifPresent(drafter -> {
                    update.set("drafterId", drafter.getId());
                    vo
                            .setDrafterName(drafter.getName())
                            .setDrafterId(drafter.getId());
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });

        Optional
                .ofNullable(dto.getDraftDepartmentId())
                .map(deptCacheManager::get)
                .ifPresent(draftDepartment -> {
                    update.set("draftDepartmentId", draftDepartment.getId());
                    vo
                            .setDraftDepartmentName(draftDepartment.getName())
                            .setDraftDepartmentId(draftDepartment.getId());
                    mongoTemplate.updateFirst(query, update, DocImport.class);
                });

        Optional.ofNullable(dto.getFolderId())
                .map(docFolderService::getById)
                .map(DocFolder::getId)
                .ifPresent(folderId -> {
                    update.set("folderId", folderId);
                    vo.setFolderId(folderId);
                    Optional.of(folderId)
                            .map(docFolderService::getFolderPath)
                            .ifPresent(vo::setFolderPath);
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });
        Optional.ofNullable(dto.getDistributeDepartmentIds())
                .filter(CollectionUtils::isNotEmpty)
                .ifPresent(distributeDepartmentIds -> {
                    Set<Long> ids = new HashSet<>();
                    Set<String> names = new HashSet<>();

                    for (Long distributeDepartmentId : distributeDepartmentIds) {
                        Optional.ofNullable(distributeDepartmentId)
                                .map(deptCacheManager::get)
                                .ifPresent(department -> {
                                    ids.add(department.getId());
                                    names.add(department.getName());
                                });
                    }

                    update.set("distributeDepartmentIds", distributeDepartmentIds);
                    vo
                            .setDistributeDepartmentIds(ids)
                            .setDistributeDepartmentNames(names);
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });

        Optional.ofNullable(dto.getApplyReason())
                .filter(StringUtils::hasText)
                .ifPresent(applyReason -> {
                    update.set("applyReason", applyReason);
                    vo.setApplyReason(applyReason);
                    mongoTemplate.updateFirst(query, update, DocImport.class);

                });
        return vo;
    }

    /**
     * 更新导入附件信息
     *
     * @param dto 请求对象
     * @return 更改后的附件信息VO
     */
    public DocImportAttachmentVO updateAttachmentItem(ImportAttachmentUpdateRequestDTO dto) {
        DocImport docImport = Optional.ofNullable(getById(dto.getDocumentId())).orElseThrow(() -> new YshNoLogException("文档不存在"));
        Map<String, DocImport.Attachment> attachments = docImport.getAttachments();
        if (attachments == null || attachments.isEmpty() || !attachments.containsKey(dto.getAttachmentId())) {
            throw new YshNoLogException("附件不存在");
        }
        DocImportAttachmentVO attachmentVO = docImportAttachmentDomainMapper.convertPO2VO(attachments.get(dto.getAttachmentId()));

        Query query = Query.query(Criteria.where("_id").is(docImport.getId()));
        Update update = new Update();
        String attachmentVersion = Optional
                .ofNullable(dto.getAttachmentVersion())
                .filter(StringUtils::hasText)
                .orElse(null);
        if (attachmentVersion != null) {
            if (VersionUtil.isValidVersion(attachmentVersion)) {
                update.set("attachments." + dto.getAttachmentId() + ".attachmentVersion", attachmentVersion);
                attachmentVO.setAttachmentVersion(attachmentVersion);
                mongoTemplate.updateFirst(query, update, DocImport.class);
            } else {
                throw new YshNoLogException("附件版本号格式错误");
            }
        }

        Optional.ofNullable(dto.getAttachmentName())
                .filter(StringUtils::hasText)
                .ifPresent(attachmentName -> {
                    update.set("attachments." + dto.getAttachmentId() + ".attachmentName", attachmentName);
                    attachmentVO.setAttachmentName(attachmentName);
                    mongoTemplate.updateFirst(query, update, DocImport.class);
                });

        Optional.ofNullable(dto.getEffectDate())
                .ifPresent(effectDate -> {
                    update.set("attachments." + dto.getAttachmentId() + ".effectDate", effectDate);
                    attachmentVO.setEffectDate(effectDate);
                    mongoTemplate.updateFirst(query, update, DocImport.class);
                });

        if (dto.getDrafterId() != null) {
            UserCacheDTO user = userCacheManager.get(dto.getDrafterId());
            if (user == null)
                throw new YshNoLogException("指定的起草人不存在");

            update.set("attachments." + dto.getAttachmentId() + ".drafterId", user.getId());
            attachmentVO.setDrafterId(user.getId());
            attachmentVO.setDrafterName(user.getName());
            mongoTemplate.updateFirst(query, update, DocImport.class);
        } else {
            Optional.ofNullable(attachmentVO.getDrafterId())
                    .map(userCacheManager::get)
                    .map(UserCacheDTO::getName)
                    .ifPresent(attachmentVO::setDrafterName);
        }
        Optional.ofNullable(dto.getDraftDepartmentId())
                .map(deptCacheManager::get)
                .ifPresentOrElse(draftDepartment -> {
                    update.set("attachments." + dto.getAttachmentId() + ".draftDepartmentId", draftDepartment.getId());
                    attachmentVO.setDraftDepartmentId(draftDepartment.getId());
                    attachmentVO.setDraftDepartmentName(draftDepartment.getName());
                    mongoTemplate.updateFirst(query, update, DocImport.class);
                }, () -> {
                    DeptCacheDTO deptCacheDTO = deptCacheManager.get(attachmentVO.getDraftDepartmentId());
                    if (deptCacheDTO != null) {
                        attachmentVO.setDraftDepartmentName(deptCacheDTO.getName());
                    }
                });

        Optional.ofNullable(dto.getAttachmentNo())
                .filter(StringUtils::hasText)
                .ifPresent(attachmentNo -> {
                    update.set("attachments." + dto.getAttachmentId() + ".attachmentNo", attachmentNo);
                    attachmentVO.setAttachmentNo(attachmentNo);
                    mongoTemplate.updateFirst(query, update, DocImport.class);
                });

        return attachmentVO;
    }

    /**
     * 检查导入记录中是否存在重复的编号+版本号
     *
     * @param docImports 导入记录列表
     * @throws YshNoLogException 如果存在重复则抛出异常
     */
    private void checkDuplicateDocuments(List<DocImport> docImports) {
        // 1. 检查系统中是否已存在相同编号+版本号的文档
        for (DocImport docImport : docImports) {
            String documentNo = docImport.getDocumentNo();
            String documentVersion = docImport.getDocumentVersion();

            if (StringUtils.hasText(documentNo) && StringUtils.hasText(documentVersion)) {
                // 检查系统中是否已存在相同编号+版本号的文档
                long existingCount = docBaseService.count(Wrappers.lambdaQuery(DocBase.class)
                        .eq(DocBase::getGroupCorpId, YshRequestTokenUtil.getRequestGroupCorpId())
                        .eq(DocBase::getNo, documentNo)
                        .eq(DocBase::getVersion, documentVersion));

                if (existingCount > 0) {
                    throw new YshNoLogException("系统中已存在编号为 " + documentNo + " 版本号为 " + documentVersion + " 的文档");
                }
            }
        }

        // 2. 检查导入记录中是否存在重复的编号+版本号
        Map<String, String> documentMap = new HashMap<>();
        for (DocImport docImport : docImports) {
            String documentNo = docImport.getDocumentNo();
            String documentVersion = docImport.getDocumentVersion();

            if (StringUtils.hasText(documentNo) && StringUtils.hasText(documentVersion)) {
                String key = documentNo + ":" + documentVersion;
                if (documentMap.containsKey(key)) {
                    throw new YshNoLogException("导入记录中存在重复的编号+版本号：" + documentNo + " (版本：" + documentVersion + ")");
                }
                documentMap.put(key, documentNo);
            }
        }
    }

}


